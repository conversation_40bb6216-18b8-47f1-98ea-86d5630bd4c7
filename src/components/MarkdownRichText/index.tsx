import React from 'react'
import type { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'
import RichText from '@/components/RichText'
import { cn } from '@/utilities/ui'

interface MarkdownRichTextProps {
  data: DefaultTypedEditorState
  enableGutter?: boolean
  enableProse?: boolean
  className?: string
}

// Convert markdown text to proper Lexical structure
const convertMarkdownToLexical = (markdownText: string): DefaultTypedEditorState => {
  const lines = markdownText.split('\n')
  const children: any[] = []

  let i = 0
  while (i < lines.length) {
    const line = lines[i].trim()

    if (!line) {
      // Empty line - add paragraph
      children.push({
        type: 'paragraph',
        children: [{ text: '' }],
      })
      i++
      continue
    }

    // Headers
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/)
    if (headerMatch) {
      const level = headerMatch[1].length
      const text = headerMatch[2]
      children.push({
        type: 'heading',
        tag: `h${level}`,
        children: [{ text: parseInlineMarkdown(text) }],
      })
      i++
      continue
    }

    // Horizontal rules
    if (line.match(/^---+$/)) {
      children.push({
        type: 'horizontalrule',
      })
      i++
      continue
    }

    // Code blocks
    if (line.startsWith('```')) {
      const language = line.substring(3).trim() || 'text'
      const codeLines = []
      i++ // Skip opening ```

      while (i < lines.length && !lines[i].trim().startsWith('```')) {
        codeLines.push(lines[i])
        i++
      }

      children.push({
        type: 'code',
        language: language,
        children: [{ text: codeLines.join('\n') }],
      })
      i++ // Skip closing ```
      continue
    }

    // Tables
    if (line.includes('|')) {
      const tableRows = []
      let currentLine = i

      // Collect all table rows
      while (currentLine < lines.length && lines[currentLine].includes('|')) {
        const row = lines[currentLine].trim()
        if (row && !row.match(/^[\|\s\-]+$/)) {
          // Skip separator rows
          const cells = row
            .split('|')
            .map((cell) => cell.trim())
            .filter((cell) => cell)
          if (cells.length > 0) {
            tableRows.push(cells)
          }
        }
        currentLine++
      }

      if (tableRows.length > 0) {
        // Create table structure
        const tableChildren = tableRows.map((row, rowIndex) => ({
          type: 'tablerow',
          children: row.map((cell) => ({
            type: rowIndex === 0 ? 'tableheader' : 'tablecell',
            children: [{ text: parseInlineMarkdown(cell) }],
          })),
        }))

        children.push({
          type: 'table',
          children: tableChildren,
        })
      }

      i = currentLine
      continue
    }

    // Lists
    const listMatch = line.match(/^\s*([-*+]|\d+\.)\s+(.+)$/)
    if (listMatch) {
      const isOrdered = /^\s*\d+\./.test(line)
      const text = listMatch[2]

      children.push({
        type: 'list',
        listType: isOrdered ? 'number' : 'bullet',
        children: [
          {
            type: 'listitem',
            children: [{ text: parseInlineMarkdown(text) }],
          },
        ],
      })
      i++
      continue
    }

    // Blockquotes
    const quoteMatch = line.match(/^\s*>\s+(.+)$/)
    if (quoteMatch) {
      const text = quoteMatch[1]
      children.push({
        type: 'quote',
        children: [{ text: parseInlineMarkdown(text) }],
      })
      i++
      continue
    }

    // Regular paragraph
    children.push({
      type: 'paragraph',
      children: [{ text: parseInlineMarkdown(line) }],
    })
    i++
  }

  return {
    root: {
      children,
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  }
}

// Parse inline markdown (bold, italic, code, links)
const parseInlineMarkdown = (text: string): string => {
  // For now, just return the text as-is since Lexical will handle the formatting
  // In a full implementation, you'd convert **bold** to proper text nodes with formatting
  return text
}

// Check if content contains markdown
const containsMarkdown = (data: DefaultTypedEditorState): boolean => {
  if (!data?.root?.children) return false

  // Check if it's a single paragraph with markdown-like text
  if (data.root.children.length === 1) {
    const firstChild = data.root.children[0]
    if (firstChild.type === 'paragraph' && firstChild.children?.length === 1) {
      const text = firstChild.children[0].text || ''
      return text.includes('#') || text.includes('**') || text.includes('|') || text.includes('```')
    }
  }

  return false
}

// Extract markdown text from Lexical structure
const extractMarkdownText = (data: DefaultTypedEditorState): string => {
  if (!data?.root?.children) return ''

  const extractText = (node: any): string => {
    if (node.text) return node.text
    if (node.children) {
      return node.children.map(extractText).join('')
    }
    return ''
  }

  return data.root.children.map(extractText).join('\n')
}

// Convert markdown to HTML
const convertMarkdownToHTML = (markdown: string): string => {
  return (
    markdown
      // Headers
      .replace(/^### (.*$)/gim, '<h3 class="text-xl font-semibold mb-4 mt-6">$1</h3>')
      .replace(/^## (.*$)/gim, '<h2 class="text-2xl font-bold mb-4 mt-8">$1</h2>')
      .replace(/^# (.*$)/gim, '<h1 class="text-3xl font-bold mb-6 mt-8">$1</h1>')
      // Bold
      .replace(/\*\*(.*?)\*\*/gim, '<strong class="font-semibold">$1</strong>')
      // Italic
      .replace(/\*(.*?)\*/gim, '<em class="italic">$1</em>')
      // Inline code
      .replace(
        /`(.*?)`/gim,
        '<code class="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono">$1</code>',
      )
      // Links
      .replace(
        /\[([^\]]*)\]\(([^\)]*)\)/gim,
        '<a href="$2" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline">$1</a>',
      )
      // Horizontal rules
      .replace(/^---+$/gim, '<hr class="border-gray-300 dark:border-gray-600 my-8" />')
      // Tables - first pass: convert rows
      .replace(/\|(.+)\|/gim, (match, content) => {
        const cells = content
          .split('|')
          .map(
            (cell: string) =>
              `<td class="border border-gray-300 dark:border-gray-600 px-4 py-2">${cell.trim()}</td>`,
          )
          .join('')
        return `<tr>${cells}</tr>`
      })
      // Line breaks and paragraphs
      .replace(/\n\n/gim, '</p><p class="mb-4">')
      .replace(/\n/gim, '<br />')
  )
}

// Wrap table rows in proper table structure
const wrapTables = (html: string): string => {
  return html.replace(
    /(<tr>.*?<\/tr>(?:\s*<tr>.*?<\/tr>)*)/gims,
    '<table class="min-w-full border-collapse border border-gray-300 dark:border-gray-600 my-6"><tbody>$1</tbody></table>',
  )
}

export default function MarkdownRichText({
  data,
  enableGutter = true,
  enableProse = true,
  className,
}: MarkdownRichTextProps) {
  // Check if the content needs markdown conversion
  if (containsMarkdown(data)) {
    const markdownText = extractMarkdownText(data)
    let htmlContent = convertMarkdownToHTML(markdownText)
    htmlContent = wrapTables(htmlContent)

    return (
      <div
        className={cn(
          'payload-richtext',
          {
            container: enableGutter,
            'max-w-none': !enableGutter,
            'mx-auto prose md:prose-md dark:prose-invert': enableProse,
          },
          className,
        )}
        dangerouslySetInnerHTML={{
          __html: `<div class="markdown-content">${htmlContent}</div>`,
        }}
      />
    )
  }

  // Use regular RichText for properly structured content
  return (
    <RichText
      data={data}
      enableGutter={enableGutter}
      enableProse={enableProse}
      className={className}
    />
  )
}
