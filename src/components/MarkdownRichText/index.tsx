import React from 'react'
import type { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'
import RichText from '@/components/RichText'
import { cn } from '@/utilities/ui'

interface MarkdownRichTextProps {
  data: DefaultTypedEditorState
  enableGutter?: boolean
  enableProse?: boolean
  className?: string
}

// Extract text content preserving line breaks and structure
const extractTextContent = (node: any): string => {
  if (!node) return ''
  if (typeof node === 'string') return node
  if (node.text) return node.text

  if (node.type === 'paragraph') {
    const text = node.children ? node.children.map(extractTextContent).join('') : ''
    return text + '\n\n'
  }

  if (node.type === 'heading') {
    const level = '#'.repeat(node.tag ? parseInt(node.tag.replace('h', '')) : 1)
    const text = node.children ? node.children.map(extractTextContent).join('') : ''
    return `${level} ${text}\n\n`
  }

  if (node.type === 'horizontalrule') {
    return '---\n\n'
  }

  if (node.type === 'code') {
    const language = node.language || 'text'
    const code = node.children ? node.children.map(extractTextContent).join('') : ''
    return `\`\`\`${language}\n${code}\n\`\`\`\n\n`
  }

  if (node.type === 'codeblock') {
    const language = node.language || 'text'
    const code = node.children ? node.children.map(extractTextContent).join('') : ''
    return `\`\`\`${language}\n${code}\n\`\`\`\n\n`
  }

  if (node.children && Array.isArray(node.children)) {
    return node.children.map(extractTextContent).join('')
  }

  return ''
}

// Convert markdown text to proper Lexical structure
const convertMarkdownToLexical = (markdownText: string): DefaultTypedEditorState => {
  const lines = markdownText.split('\n')
  const children: any[] = []

  let i = 0
  while (i < lines.length) {
    const line = lines[i].trim()

    if (!line) {
      // Empty line - add paragraph
      children.push({
        type: 'paragraph',
        children: [{ text: '' }],
      })
      i++
      continue
    }

    // Headers
    const headerMatch = line.match(/^(#{1,6})\s+(.+)$/)
    if (headerMatch) {
      const level = headerMatch[1].length
      const text = headerMatch[2]
      children.push({
        type: 'heading',
        tag: `h${level}`,
        children: [{ text: parseInlineMarkdown(text) }],
      })
      i++
      continue
    }

    // Horizontal rules
    if (line.match(/^---+$/)) {
      children.push({
        type: 'horizontalrule',
      })
      i++
      continue
    }

    // Code blocks
    if (line.startsWith('```')) {
      const language = line.substring(3).trim() || 'text'
      const codeLines = []
      i++ // Skip opening ```

      while (i < lines.length && !lines[i].trim().startsWith('```')) {
        codeLines.push(lines[i])
        i++
      }

      children.push({
        type: 'code',
        language: language,
        children: [{ text: codeLines.join('\n') }],
      })
      i++ // Skip closing ```
      continue
    }

    // Tables
    if (line.includes('|')) {
      const tableRows = []
      let currentLine = i

      // Collect all table rows
      while (currentLine < lines.length && lines[currentLine].includes('|')) {
        const row = lines[currentLine].trim()
        if (row && !row.match(/^[\|\s\-]+$/)) {
          // Skip separator rows
          const cells = row
            .split('|')
            .map((cell) => cell.trim())
            .filter((cell) => cell)
          if (cells.length > 0) {
            tableRows.push(cells)
          }
        }
        currentLine++
      }

      if (tableRows.length > 0) {
        // Create table structure
        const tableChildren = tableRows.map((row, rowIndex) => ({
          type: 'tablerow',
          children: row.map((cell) => ({
            type: rowIndex === 0 ? 'tableheader' : 'tablecell',
            children: [{ text: parseInlineMarkdown(cell) }],
          })),
        }))

        children.push({
          type: 'table',
          children: tableChildren,
        })
      }

      i = currentLine
      continue
    }

    // Lists
    const listMatch = line.match(/^\s*([-*+]|\d+\.)\s+(.+)$/)
    if (listMatch) {
      const isOrdered = /^\s*\d+\./.test(line)
      const text = listMatch[2]

      children.push({
        type: 'list',
        listType: isOrdered ? 'number' : 'bullet',
        children: [
          {
            type: 'listitem',
            children: [{ text: parseInlineMarkdown(text) }],
          },
        ],
      })
      i++
      continue
    }

    // Blockquotes
    const quoteMatch = line.match(/^\s*>\s+(.+)$/)
    if (quoteMatch) {
      const text = quoteMatch[1]
      children.push({
        type: 'quote',
        children: [{ text: parseInlineMarkdown(text) }],
      })
      i++
      continue
    }

    // Regular paragraph
    children.push({
      type: 'paragraph',
      children: [{ text: parseInlineMarkdown(line) }],
    })
    i++
  }

  return {
    root: {
      children,
      direction: 'ltr',
      format: '',
      indent: 0,
      type: 'root',
      version: 1,
    },
  }
}

// Parse inline markdown (bold, italic, code, links)
const parseInlineMarkdown = (text: string): string => {
  // For now, just return the text as-is since Lexical will handle the formatting
  // In a full implementation, you'd convert **bold** to proper text nodes with formatting
  return text
}

// Check if content contains markdown
const containsMarkdown = (data: DefaultTypedEditorState): boolean => {
  if (!data?.root?.children) return false

  // Check if it's a single paragraph with markdown-like text
  if (data.root.children.length === 1) {
    const firstChild = data.root.children[0]
    if (firstChild.type === 'paragraph' && firstChild.children?.length === 1) {
      const text = firstChild.children[0].text || ''
      return text.includes('#') || text.includes('**') || text.includes('|') || text.includes('```')
    }
  }

  return false
}

// Extract markdown text from Lexical structure
const extractMarkdownText = (data: DefaultTypedEditorState): string => {
  if (!data?.root?.children) return ''

  const extractText = (node: any): string => {
    if (node.text) return node.text
    if (node.children) {
      return node.children.map(extractText).join('')
    }
    return ''
  }

  return data.root.children.map(extractText).join('\n')
}

// Convert markdown-like text to HTML with advanced processing
const convertToHTML = (text: string): string => {
  const lines = text.split('\n')
  const processedLines: string[] = []

  for (let i = 0; i < lines.length; i++) {
    let line = lines[i]

    if (!line.trim()) {
      processedLines.push('')
      continue
    }

    // Code blocks
    if (line.trim().startsWith('```')) {
      const language = line.trim().substring(3).trim() || 'text'
      const codeLines = []
      i++

      while (i < lines.length && !lines[i].trim().startsWith('```')) {
        codeLines.push(lines[i])
        i++
      }

      const codeContent = codeLines.join('\n')
      processedLines.push(`
        <div class="my-6">
          <pre class="bg-gray-900 text-gray-100 p-4 rounded-lg overflow-x-auto">
            <code class="language-${language}">${escapeHtml(codeContent)}</code>
          </pre>
        </div>
      `)
      continue
    }

    // Headers
    if (line.match(/^#{1,6}\s+/)) {
      const headerMatch = line.match(/^(#{1,6})\s+(.+)$/)
      if (headerMatch) {
        const level = headerMatch[1].length
        const text = headerMatch[2]
        const classes = {
          1: 'text-3xl font-bold mb-6 mt-8',
          2: 'text-2xl font-bold mb-4 mt-8',
          3: 'text-xl font-semibold mb-4 mt-6',
          4: 'text-lg font-semibold mb-3 mt-5',
          5: 'text-base font-semibold mb-2 mt-4',
          6: 'text-sm font-semibold mb-2 mt-3',
        }
        processedLines.push(
          `<h${level} class="${classes[level as keyof typeof classes]}">${text}</h${level}>`,
        )
        continue
      }
    }

    // Horizontal rules
    if (line.match(/^---+$/)) {
      processedLines.push('<hr class="border-gray-300 dark:border-gray-600 my-8" />')
      continue
    }

    // Tables
    if (line.includes('|')) {
      if (line.match(/^\s*\|\s*[\-\s:]+\s*\|\s*[\-\s:]*\s*\|\s*$/)) {
        continue
      }

      const cells = line
        .split('|')
        .map((cell) => cell.trim())
        .filter((cell) => cell)

      if (cells.length > 1) {
        const isHeaderRow =
          i > 0 &&
          lines[i - 1].match(/^#{1,6}\s+/) &&
          cells.some((cell) => cell.includes('Feature') || cell.includes('Term'))

        const cellsHtml = cells
          .map((cell) => {
            const tag = isHeaderRow ? 'th' : 'td'
            const classes = isHeaderRow
              ? 'border border-gray-300 dark:border-gray-600 px-4 py-2 bg-gray-50 dark:bg-gray-700 font-semibold'
              : 'border border-gray-300 dark:border-gray-600 px-4 py-2'
            return `<${tag} class="${classes}">${processInlineMarkdown(cell)}</${tag}>`
          })
          .join('')
        processedLines.push(`<tr>${cellsHtml}</tr>`)
        continue
      }
    }

    // Regular paragraphs
    if (line.trim()) {
      processedLines.push(`<p class="mb-4">${processInlineMarkdown(line)}</p>`)
    }
  }

  return processedLines.join('\n')
}

// Process inline markdown
const processInlineMarkdown = (text: string): string => {
  return text
    .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
    .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
    .replace(
      /`(.*?)`/g,
      '<code class="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono">$1</code>',
    )
    .replace(
      /\[([^\]]*)\]\(([^\)]*)\)/g,
      '<a href="$2" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline">$1</a>',
    )
}

// Escape HTML
const escapeHtml = (text: string): string => {
  return text
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#39;')
}

// Wrap tables
const wrapTables = (html: string): string => {
  return html.replace(/(<tr>.*?<\/tr>(?:\s*<tr>.*?<\/tr>)*)/gims, (match, tableRows) => {
    const hasHeaderRow = tableRows.includes('<th')

    if (hasHeaderRow) {
      const rows = tableRows.match(/<tr>.*?<\/tr>/gims) || []
      const headerRow = rows[0] || ''
      const bodyRows = rows.slice(1).join('\n')

      return `<div class="overflow-x-auto my-6">
        <table class="min-w-full border-collapse border border-gray-300 dark:border-gray-600">
          <thead>${headerRow}</thead>
          <tbody>${bodyRows}</tbody>
        </table>
      </div>`
    } else {
      return `<div class="overflow-x-auto my-6">
        <table class="min-w-full border-collapse border border-gray-300 dark:border-gray-600">
          <tbody>${tableRows}</tbody>
        </table>
      </div>`
    }
  })
}

export default function MarkdownRichText({
  data,
  enableGutter = true,
  enableProse = true,
  className,
}: MarkdownRichTextProps) {
  // Check if the content needs markdown conversion
  if (containsMarkdown(data)) {
    const markdownText = extractTextContent(data.root)
    let htmlContent = convertToHTML(markdownText)
    htmlContent = wrapTables(htmlContent)

    return (
      <div
        className={cn(
          'payload-richtext',
          {
            container: enableGutter,
            'max-w-none': !enableGutter,
            'mx-auto prose md:prose-md dark:prose-invert': enableProse,
          },
          className,
        )}
        dangerouslySetInnerHTML={{
          __html: htmlContent,
        }}
      />
    )
  }

  // Use regular RichText for properly structured content
  return (
    <RichText
      data={data}
      enableGutter={enableGutter}
      enableProse={enableProse}
      className={className}
    />
  )
}
