'use client'

import React from 'react'

// Simple test component to verify the field integration works
export const SimplePreviewField: React.FC = () => {
  console.log('SimplePreviewField component is rendering!')

  return (
    <div
      style={{
        marginBottom: '16px',
        padding: '12px',
        backgroundColor: '#f0f9ff',
        border: '2px solid #0ea5e9',
        borderRadius: '6px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      }}
    >
      <div style={{ marginBottom: '8px' }}>
        <h4
          style={{
            margin: '0 0 4px 0',
            fontSize: '14px',
            fontWeight: '600',
            color: '#0369a1',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
          }}
        >
          📖 Content Preview (Test Component)
        </h4>
        <p
          style={{
            margin: '0',
            fontSize: '12px',
            color: '#0284c7',
          }}
        >
          This is a test component to verify the preview field integration is working.
        </p>
      </div>

      <button
        style={{
          padding: '8px 16px',
          backgroundColor: '#0ea5e9',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          fontSize: '12px',
          cursor: 'pointer',
          fontWeight: '500',
        }}
        onClick={() => alert('Preview component is working! 🎉')}
      >
        👁️ Test Preview Button
      </button>

      <div
        style={{
          marginTop: '8px',
          fontSize: '11px',
          color: '#6b7280',
          fontStyle: 'italic',
        }}
      >
        💡 If you can see this, the preview field integration is working correctly!
      </div>
    </div>
  )
}

// Export both named and default
export default SimplePreviewField
