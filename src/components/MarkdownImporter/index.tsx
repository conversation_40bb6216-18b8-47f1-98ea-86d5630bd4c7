'use client'

import React, { useState } from 'react'
import { Button } from '@payloadcms/ui'

interface MarkdownImporterProps {
  onImport?: (markdown: string) => void
}

// Simple markdown to HTML converter for basic syntax (currently unused)
// const convertMarkdownToHTML = (markdown: string): string => {
//   const html = markdown
//     // Headers
//     .replace(/^### (.*$)/gim, '<h3>$1</h3>')
//     .replace(/^## (.*$)/gim, '<h2>$1</h2>')
//     .replace(/^# (.*$)/gim, '<h1>$1</h1>')
//     // Bold
//     .replace(/\*\*(.*)\*\*/gim, '<strong>$1</strong>')
//     // Italic
//     .replace(/\*(.*)\*/gim, '<em>$1</em>')
//     // Inline code
//     .replace(/`(.*?)`/gim, '<code>$1</code>')
//     // Links
//     .replace(/\[([^\]]*)\]\(([^\)]*)\)/gim, '<a href="$2">$1</a>')
//     // Line breaks
//     .replace(/\n$/gim, '<br />')

//   return html
// }

export const MarkdownImporter: React.FC<MarkdownImporterProps> = ({ onImport }) => {
  const [isOpen, setIsOpen] = useState(false)
  const [markdown, setMarkdown] = useState('')

  const handleImport = () => {
    if (markdown.trim()) {
      if (onImport) {
        onImport(markdown)
      } else {
        // Fallback: copy to clipboard with instructions
        navigator.clipboard
          .writeText(markdown)
          .then(() => {
            alert(
              'Markdown copied to clipboard! You can now paste it into the editor and use the built-in Markdown shortcuts.',
            )
          })
          .catch(() => {
            alert('Please copy the markdown text manually and paste it into the editor.')
          })
      }
      setMarkdown('')
      setIsOpen(false)
    }
  }

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file && file.type === 'text/markdown') {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        setMarkdown(content)
      }
      reader.readAsText(file)
    }
  }

  if (!isOpen) {
    return (
      <Button onClick={() => setIsOpen(true)} size="small" buttonStyle="secondary">
        📝 Import Markdown
      </Button>
    )
  }

  return (
    <div
      style={{
        border: '1px solid #e1e5e9',
        borderRadius: '4px',
        padding: '16px',
        marginBottom: '16px',
        backgroundColor: '#f8f9fa',
      }}
    >
      <h4 style={{ margin: '0 0 12px 0', fontSize: '14px', fontWeight: '600' }}>
        📝 Import Markdown Content
      </h4>
      <p style={{ margin: '0 0 12px 0', fontSize: '12px', color: '#6b7280' }}>
        Import Markdown and copy to clipboard, then paste into the editor for auto-conversion.
      </p>

      <div style={{ marginBottom: '12px' }}>
        <label
          style={{ display: 'block', marginBottom: '4px', fontSize: '12px', fontWeight: '500' }}
        >
          Upload .md file:
        </label>
        <input
          type="file"
          accept=".md,.markdown"
          onChange={handleFileUpload}
          style={{ fontSize: '12px' }}
        />
      </div>

      <div style={{ marginBottom: '12px' }}>
        <label
          style={{ display: 'block', marginBottom: '4px', fontSize: '12px', fontWeight: '500' }}
        >
          Or paste Markdown text:
        </label>
        <textarea
          value={markdown}
          onChange={(e) => setMarkdown(e.target.value)}
          placeholder="Paste your Markdown content here..."
          style={{
            width: '100%',
            height: '200px',
            padding: '8px',
            border: '1px solid #d1d5db',
            borderRadius: '4px',
            fontSize: '12px',
            fontFamily: 'Monaco, Consolas, "Courier New", monospace',
            resize: 'vertical',
          }}
        />
      </div>

      <div style={{ display: 'flex', gap: '8px' }}>
        <Button onClick={handleImport} size="small" disabled={!markdown.trim()}>
          📋 Copy to Clipboard
        </Button>
        <Button
          onClick={() => {
            setIsOpen(false)
            setMarkdown('')
          }}
          size="small"
          buttonStyle="secondary"
        >
          Cancel
        </Button>
      </div>

      <div style={{ marginTop: '12px', fontSize: '11px', color: '#6b7280' }}>
        <strong>How it works:</strong>
        <ol style={{ margin: '4px 0', paddingLeft: '16px' }}>
          <li>Upload .md file or paste Markdown text above</li>
          <li>Click &quot;📋 Copy to Clipboard&quot;</li>
          <li>Paste into the rich text editor below</li>
          <li>Markdown will auto-convert as you type!</li>
        </ol>
        <strong>Supported Markdown:</strong>
        <ul style={{ margin: '4px 0', paddingLeft: '16px' }}>
          <li># Headings (H1-H6)</li>
          <li>**Bold** and *Italic* text</li>
          <li>`Inline code` and ```code blocks```</li>
          <li>- Lists and 1. Numbered lists</li>
          <li>&gt; Blockquotes</li>
          <li>[Links](url) and ![Images](url)</li>
          <li>--- Horizontal rules</li>
        </ul>
      </div>
    </div>
  )
}
