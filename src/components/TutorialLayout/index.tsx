import React from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ChevronLeft, ChevronRight, Clock, BookOpen, Code, Play } from 'lucide-react'
import RichText from '@/components/RichText'

interface Tutorial {
  id: string
  title: string
  excerpt?: string
  content: any
  category: {
    title: string
    slug: string
    color?: string
  }
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime?: string
  codeExamples?: Array<{
    title: string
    language: string
    code: string
    tryItYourself: boolean
    expectedOutput?: string
  }>
  previousTutorial?: {
    title: string
    slug: string
  }
  nextTutorial?: {
    title: string
    slug: string
  }
  relatedTutorials?: Array<{
    title: string
    slug: string
    excerpt?: string
  }>
}

interface TutorialSidebarItem {
  title: string
  slug: string
  isActive?: boolean
  children?: TutorialSidebarItem[]
}

interface TutorialLayoutProps {
  tutorial: Tutorial
  sidebarItems: TutorialSidebarItem[]
}

const difficultyColors = {
  beginner: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
  intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
  advanced: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
}

const CodeEditor: React.FC<{ 
  code: string
  language: string
  title: string
  tryItYourself: boolean
  expectedOutput?: string
}> = ({ code, language, title, tryItYourself, expectedOutput }) => {
  const [userCode, setUserCode] = React.useState(code)
  const [showOutput, setShowOutput] = React.useState(false)

  return (
    <div className="my-6">
      <div className="flex items-center justify-between mb-3">
        <h4 className="text-lg font-semibold">{title}</h4>
        {tryItYourself && (
          <Button 
            onClick={() => setShowOutput(!showOutput)}
            size="sm"
            className="bg-green-600 hover:bg-green-700"
          >
            <Play className="h-4 w-4 mr-2" />
            Try it Yourself
          </Button>
        )}
      </div>
      
      <div className="border rounded-lg overflow-hidden">
        <div className="bg-gray-800 text-white p-3 text-sm font-mono">
          <div className="flex items-center justify-between mb-2">
            <span className="text-gray-300">{language.toUpperCase()}</span>
            <div className="flex space-x-2">
              <div className="w-3 h-3 bg-red-500 rounded-full"></div>
              <div className="w-3 h-3 bg-yellow-500 rounded-full"></div>
              <div className="w-3 h-3 bg-green-500 rounded-full"></div>
            </div>
          </div>
          {tryItYourself ? (
            <textarea
              value={userCode}
              onChange={(e) => setUserCode(e.target.value)}
              className="w-full bg-transparent border-none outline-none resize-none font-mono text-sm"
              rows={code.split('\n').length}
            />
          ) : (
            <pre className="text-green-400 whitespace-pre-wrap">{code}</pre>
          )}
        </div>
        
        {tryItYourself && showOutput && expectedOutput && (
          <div className="bg-gray-100 dark:bg-gray-700 p-3 border-t">
            <div className="text-sm font-semibold mb-2">Output:</div>
            <pre className="text-sm">{expectedOutput}</pre>
          </div>
        )}
      </div>
    </div>
  )
}

const Sidebar: React.FC<{ items: TutorialSidebarItem[] }> = ({ items }) => {
  return (
    <div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 h-screen sticky top-0 overflow-y-auto">
      <div className="p-4">
        <h3 className="font-semibold text-lg mb-4">Tutorial Contents</h3>
        <nav>
          {items.map((item, index) => (
            <div key={index} className="mb-2">
              <Link
                href={`/tutorials/${item.slug}`}
                className={`block px-3 py-2 rounded-md text-sm transition-colors ${
                  item.isActive
                    ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                }`}
              >
                {item.title}
              </Link>
              {item.children && (
                <div className="ml-4 mt-1">
                  {item.children.map((child, childIndex) => (
                    <Link
                      key={childIndex}
                      href={`/tutorials/${child.slug}`}
                      className={`block px-3 py-1 rounded-md text-xs transition-colors ${
                        child.isActive
                          ? 'bg-blue-50 text-blue-600 dark:bg-blue-900 dark:text-blue-400'
                          : 'text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                    >
                      {child.title}
                    </Link>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>
      </div>
    </div>
  )
}

export const TutorialLayout: React.FC<TutorialLayoutProps> = ({
  tutorial,
  sidebarItems,
}) => {
  return (
    <div className="flex min-h-screen">
      <Sidebar items={sidebarItems} />
      
      <div className="flex-1 max-w-4xl mx-auto p-8">
        {/* Breadcrumb */}
        <nav className="mb-6">
          <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <Link href="/" className="hover:text-blue-600">Home</Link>
            <span>/</span>
            <Link href="/tutorials" className="hover:text-blue-600">Tutorials</Link>
            <span>/</span>
            <Link 
              href={`/${tutorial.category.slug}`} 
              className="hover:text-blue-600"
            >
              {tutorial.category.title}
            </Link>
            <span>/</span>
            <span className="text-gray-900 dark:text-gray-100">{tutorial.title}</span>
          </div>
        </nav>

        {/* Tutorial Header */}
        <header className="mb-8">
          <div className="flex items-center gap-3 mb-4">
            <Badge 
              variant="secondary"
              style={{ 
                backgroundColor: tutorial.category.color + '20', 
                color: tutorial.category.color 
              }}
            >
              {tutorial.category.title}
            </Badge>
            <Badge className={difficultyColors[tutorial.difficulty]}>
              {tutorial.difficulty}
            </Badge>
            {tutorial.estimatedTime && (
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Clock className="h-4 w-4 mr-1" />
                {tutorial.estimatedTime}
              </div>
            )}
          </div>
          
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {tutorial.title}
          </h1>
          
          {tutorial.excerpt && (
            <p className="text-lg text-gray-600 dark:text-gray-300 leading-relaxed">
              {tutorial.excerpt}
            </p>
          )}
        </header>

        {/* Tutorial Content */}
        <div className="prose dark:prose-invert max-w-none mb-8">
          <RichText data={tutorial.content} />
        </div>

        {/* Code Examples */}
        {tutorial.codeExamples && tutorial.codeExamples.length > 0 && (
          <div className="mb-8">
            {tutorial.codeExamples.map((example, index) => (
              <CodeEditor
                key={index}
                code={example.code}
                language={example.language}
                title={example.title}
                tryItYourself={example.tryItYourself}
                expectedOutput={example.expectedOutput}
              />
            ))}
          </div>
        )}

        {/* Navigation */}
        <div className="flex justify-between items-center py-8 border-t border-gray-200 dark:border-gray-700">
          {tutorial.previousTutorial ? (
            <Link href={`/tutorials/${tutorial.previousTutorial.slug}`}>
              <Button variant="outline" className="flex items-center">
                <ChevronLeft className="h-4 w-4 mr-2" />
                {tutorial.previousTutorial.title}
              </Button>
            </Link>
          ) : (
            <div></div>
          )}
          
          {tutorial.nextTutorial ? (
            <Link href={`/tutorials/${tutorial.nextTutorial.slug}`}>
              <Button className="flex items-center">
                {tutorial.nextTutorial.title}
                <ChevronRight className="h-4 w-4 ml-2" />
              </Button>
            </Link>
          ) : (
            <div></div>
          )}
        </div>

        {/* Related Tutorials */}
        {tutorial.relatedTutorials && tutorial.relatedTutorials.length > 0 && (
          <div className="mt-12">
            <h3 className="text-2xl font-bold mb-6">Related Tutorials</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {tutorial.relatedTutorials.map((related, index) => (
                <Link key={index} href={`/tutorials/${related.slug}`}>
                  <Card className="hover:shadow-md transition-shadow">
                    <CardHeader>
                      <CardTitle className="text-lg">{related.title}</CardTitle>
                      {related.excerpt && (
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {related.excerpt}
                        </p>
                      )}
                    </CardHeader>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
