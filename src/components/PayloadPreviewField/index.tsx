'use client'

import React, { useEffect, useState } from 'react'
import { RichTextPreview } from '@/components/RichTextPreview'
import type { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'

interface PayloadPreviewFieldProps {
  /** Field name to watch for changes */
  fieldName?: string
  /** Custom title for the preview */
  title?: string
  /** Custom description */
  description?: string
  /** Size of the preview modal */
  size?: 'small' | 'medium' | 'large' | 'fullscreen'
  /** Show word/character counts */
  showStats?: boolean
  /** Custom button text */
  buttonText?: string
}

// This component is specifically designed for Payload CMS admin interface
export const PayloadPreviewField: React.FC<PayloadPreviewFieldProps> = ({
  fieldName = 'content',
  title,
  description = 'Preview how your content will appear on the frontend',
  size = 'large',
  showStats = true,
  buttonText = '👁️ Preview Content',
}) => {
  const [richTextData, setRichTextData] = useState<DefaultTypedEditorState | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Determine title based on field name if not provided
  const previewTitle = title || `${fieldName.charAt(0).toUpperCase() + fieldName.slice(1)} Preview`

  useEffect(() => {
    let timeoutId: NodeJS.Timeout

    const extractRichTextData = () => {
      setIsLoading(true)

      try {
        // Method 1: Try to find Lexical editor instances
        const lexicalEditors = document.querySelectorAll('[data-lexical-editor="true"]')

        for (const editorElement of lexicalEditors) {
          const editor = (editorElement as any).__lexicalEditor
          if (editor) {
            const editorState = editor.getEditorState()
            if (editorState) {
              const jsonState = editorState.toJSON()
              if (jsonState && jsonState.root && jsonState.root.children.length > 0) {
                setRichTextData(jsonState)
                setIsLoading(false)
                return
              }
            }
          }
        }

        // Method 2: Try to find rich text content in form data
        const richTextElements = document.querySelectorAll(
          '[data-field-name*="richText"], [data-field-name*="content"]',
        )

        for (const element of richTextElements) {
          const fieldData = (element as any).value
          if (fieldData) {
            try {
              const parsedData = JSON.parse(fieldData)
              if (parsedData && parsedData.root) {
                setRichTextData(parsedData)
                setIsLoading(false)
                return
              }
            } catch (e) {
              // Continue to next method
            }
          }
        }

        // Method 3: Look for hidden inputs with rich text data
        const hiddenInputs = document.querySelectorAll('input[type="hidden"]')

        for (const input of hiddenInputs) {
          const inputElement = input as HTMLInputElement
          if (inputElement.name.includes(fieldName) || inputElement.name.includes('richText')) {
            try {
              const parsedData = JSON.parse(inputElement.value)
              if (parsedData && parsedData.root) {
                setRichTextData(parsedData)
                setIsLoading(false)
                return
              }
            } catch (e) {
              // Continue to next method
            }
          }
        }

        // Method 4: Create sample data if no content found
        setRichTextData({
          root: {
            children: [
              {
                type: 'paragraph',
                children: [
                  { text: 'No content available for preview. Start typing in the editor above!' },
                ],
              },
            ],
            direction: 'ltr',
            format: '',
            indent: 0,
            type: 'root',
            version: 1,
          },
        })
      } catch (error) {
        console.warn('Could not extract rich text data:', error)
        setRichTextData(null)
      } finally {
        setIsLoading(false)
      }
    }

    // Initial extraction with delay to ensure DOM is ready
    timeoutId = setTimeout(extractRichTextData, 500)

    // Set up observers for changes
    const observer = new MutationObserver(() => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(extractRichTextData, 300)
    })

    // Observe the entire document for changes
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['value', 'data-lexical-editor'],
    })

    // Listen for form events
    const handleFormChange = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(extractRichTextData, 200)
    }

    document.addEventListener('input', handleFormChange)
    document.addEventListener('change', handleFormChange)
    document.addEventListener('keyup', handleFormChange)

    return () => {
      clearTimeout(timeoutId)
      observer.disconnect()
      document.removeEventListener('input', handleFormChange)
      document.removeEventListener('change', handleFormChange)
      document.removeEventListener('keyup', handleFormChange)
    }
  }, [fieldName])

  return (
    <div
      style={{
        marginBottom: '16px',
        padding: '12px',
        backgroundColor: '#f0f9ff',
        border: '1px solid #0ea5e9',
        borderRadius: '6px',
        boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      }}
    >
      <div style={{ marginBottom: '8px' }}>
        <h4
          style={{
            margin: '0 0 4px 0',
            fontSize: '14px',
            fontWeight: '600',
            color: '#0369a1',
            display: 'flex',
            alignItems: 'center',
            gap: '6px',
          }}
        >
          📖 {previewTitle}
          {isLoading && (
            <span
              style={{
                fontSize: '12px',
                color: '#6b7280',
                fontWeight: 'normal',
              }}
            >
              (Loading...)
            </span>
          )}
        </h4>
        {description && (
          <p
            style={{
              margin: '0',
              fontSize: '12px',
              color: '#0284c7',
            }}
          >
            {description}
          </p>
        )}
      </div>

      <RichTextPreview
        data={richTextData}
        title={previewTitle}
        size={size}
        showWordCount={showStats}
        showCharCount={showStats}
        buttonText={buttonText}
        className="payload-preview-field"
      />

      <div
        style={{
          marginTop: '8px',
          fontSize: '11px',
          color: '#6b7280',
          fontStyle: 'italic',
        }}
      >
        💡 This preview updates automatically as you edit the content above
      </div>
    </div>
  )
}

// Export as default for easier importing
export default PayloadPreviewField
