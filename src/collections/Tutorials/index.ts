import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
import { authenticatedOrPublished } from '../../access/authenticatedOrPublished'
import { slugField } from '@/fields/slug'
import { populatePublishedAt } from '@/hooks/populatePublishedAt'
// Simple revalidation hook inline for now
const revalidatePage = () => {}

import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField,
} from '@payloadcms/plugin-seo/fields'

import { lexicalEditor } from '@payloadcms/richtext-lexical'
import {
  FixedToolbarFeature,
  InlineToolbarFeature,
  HeadingFeature,
  BoldFeature,
  ItalicFeature,
  UnderlineFeature,
  StrikethroughFeature,
  SubscriptFeature,
  SuperscriptFeature,
  InlineCodeFeature,
  ParagraphFeature,
  ChecklistFeature,
  OrderedListFeature,
  UnorderedListFeature,
  IndentFeature,
  AlignFeature,
  BlockquoteFeature,
  RelationshipFeature,
  LinkFeature,
  UploadFeature,
  HorizontalRuleFeature,
  BlocksFeature,
} from '@payloadcms/richtext-lexical'

export const Tutorials: CollectionConfig<'tutorials'> = {
  slug: 'tutorials',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  defaultPopulate: {
    title: true,
    slug: true,
    category: true,
    excerpt: true,
  },
  admin: {
    defaultColumns: ['title', 'category', 'status', 'updatedAt'],
    useAsTitle: 'title',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'excerpt',
      type: 'textarea',
      admin: {
        description: 'Brief description of what this tutorial covers',
      },
    },
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'categories',
      required: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'chapter',
      type: 'relationship',
      relationTo: 'chapters',
      admin: {
        description: 'Chapter this tutorial belongs to (optional)',
        position: 'sidebar',
      },
    },
    {
      name: 'lessonNumber',
      type: 'number',
      admin: {
        description: 'Lesson number within the chapter (1, 2, 3...)',
        position: 'sidebar',
        condition: (_, siblingData) => siblingData?.chapter,
      },
    },

    {
      name: 'difficulty',
      type: 'select',
      options: [
        { label: 'Beginner', value: 'beginner' },
        { label: 'Intermediate', value: 'intermediate' },
        { label: 'Advanced', value: 'advanced' },
      ],
      defaultValue: 'beginner',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'estimatedTime',
      type: 'text',
      admin: {
        description: 'e.g., "15 minutes", "1 hour"',
        position: 'sidebar',
      },
    },
    {
      name: 'order',
      type: 'number',
      admin: {
        description: 'Order within the category (lower numbers appear first)',
        position: 'sidebar',
      },
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: 'Content',
          fields: [
            {
              name: 'markdownHelp',
              type: 'ui',
              admin: {
                components: {
                  Field: {
                    path: '@/components/MarkdownInstructions',
                    exportName: 'MarkdownInstructions',
                  },
                },
              },
            },
            {
              name: 'markdownImport',
              type: 'ui',
              admin: {
                components: {
                  Field: {
                    path: '@/components/MarkdownImporter',
                    exportName: 'MarkdownImporter',
                  },
                },
                // description: 'Import Markdown files or paste Markdown text to copy to clipboard',
              },
            },
            {
              name: 'contentPreview',
              type: 'ui',
              admin: {
                components: {
                  Field: {
                    path: '@/components/PayloadPreviewField',
                    exportName: 'PayloadPreviewField',
                  },
                },
                description: 'Preview how your tutorial content will appear to students',
              },
            },
            {
              name: 'content',
              type: 'richText',
              required: true,
              editor: lexicalEditor({
                features: ({ rootFeatures }) => {
                  return [
                    ...rootFeatures,
                    // Toolbar Features
                    FixedToolbarFeature(),
                    InlineToolbarFeature(),

                    // Note: Lexical has built-in Markdown shortcuts (type ## for H2, **bold**, etc.)

                    // Text Formatting
                    HeadingFeature({ enabledHeadingSizes: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6'] }),
                    ParagraphFeature(),
                    BoldFeature(),
                    ItalicFeature(),
                    UnderlineFeature(),
                    StrikethroughFeature(),
                    SubscriptFeature(),
                    SuperscriptFeature(),
                    InlineCodeFeature(),

                    // Lists and Structure
                    OrderedListFeature(),
                    UnorderedListFeature(),
                    ChecklistFeature(),
                    IndentFeature(),

                    // Alignment
                    AlignFeature(),

                    // Content Blocks
                    BlockquoteFeature(),
                    HorizontalRuleFeature(),

                    // Media and Links
                    LinkFeature({
                      enabledCollections: ['pages', 'posts', 'tutorials', 'references'],
                    }),
                    UploadFeature({
                      collections: {
                        media: {
                          fields: [
                            {
                              name: 'alt',
                              type: 'text',
                              required: true,
                            },
                            {
                              name: 'caption',
                              type: 'text',
                            },
                          ],
                        },
                      },
                    }),

                    // Advanced Features
                    RelationshipFeature({
                      enabledCollections: ['tutorials', 'references', 'posts'],
                    }),

                    // Custom Blocks for Code Examples
                    BlocksFeature({
                      blocks: [
                        {
                          slug: 'codeBlock',
                          labels: {
                            singular: 'Code Block',
                            plural: 'Code Blocks',
                          },
                          fields: [
                            {
                              name: 'language',
                              type: 'select',
                              required: true,
                              options: [
                                { label: 'HTML', value: 'html' },
                                { label: 'CSS', value: 'css' },
                                { label: 'JavaScript', value: 'javascript' },
                                { label: 'TypeScript', value: 'typescript' },
                                { label: 'Python', value: 'python' },
                                { label: 'JSON', value: 'json' },
                                { label: 'Bash', value: 'bash' },
                                { label: 'SQL', value: 'sql' },
                                { label: 'PHP', value: 'php' },
                                { label: 'Java', value: 'java' },
                                { label: 'C++', value: 'cpp' },
                                { label: 'C#', value: 'csharp' },
                                { label: 'Go', value: 'go' },
                                { label: 'Rust', value: 'rust' },
                                { label: 'Swift', value: 'swift' },
                                { label: 'Kotlin', value: 'kotlin' },
                                { label: 'Dart', value: 'dart' },
                                { label: 'Ruby', value: 'ruby' },
                                { label: 'YAML', value: 'yaml' },
                                { label: 'XML', value: 'xml' },
                                { label: 'Markdown', value: 'markdown' },
                                { label: 'Plain Text', value: 'text' },
                              ],
                            },
                            {
                              name: 'code',
                              type: 'code',
                              required: true,
                            },
                            {
                              name: 'filename',
                              type: 'text',
                              admin: {
                                description: 'Optional filename to display (e.g., index.html)',
                              },
                            },
                            {
                              name: 'highlightLines',
                              type: 'text',
                              admin: {
                                description: 'Lines to highlight (e.g., "1,3-5,8")',
                              },
                            },
                          ],
                        },
                        {
                          slug: 'callout',
                          labels: {
                            singular: 'Callout',
                            plural: 'Callouts',
                          },
                          fields: [
                            {
                              name: 'type',
                              type: 'select',
                              required: true,
                              options: [
                                { label: 'Info', value: 'info' },
                                { label: 'Warning', value: 'warning' },
                                { label: 'Error', value: 'error' },
                                { label: 'Success', value: 'success' },
                                { label: 'Tip', value: 'tip' },
                                { label: 'Note', value: 'note' },
                              ],
                            },
                            {
                              name: 'title',
                              type: 'text',
                            },
                            {
                              name: 'content',
                              type: 'richText',
                              editor: lexicalEditor({
                                features: ({ rootFeatures }) => {
                                  return [
                                    ...rootFeatures,
                                    BoldFeature(),
                                    ItalicFeature(),
                                    InlineCodeFeature(),
                                    LinkFeature(),
                                  ]
                                },
                              }),
                            },
                          ],
                        },
                        {
                          slug: 'imageComparison',
                          labels: {
                            singular: 'Image Comparison',
                            plural: 'Image Comparisons',
                          },
                          fields: [
                            {
                              name: 'beforeImage',
                              type: 'upload',
                              relationTo: 'media',
                              required: true,
                            },
                            {
                              name: 'afterImage',
                              type: 'upload',
                              relationTo: 'media',
                              required: true,
                            },
                            {
                              name: 'beforeLabel',
                              type: 'text',
                              defaultValue: 'Before',
                            },
                            {
                              name: 'afterLabel',
                              type: 'text',
                              defaultValue: 'After',
                            },
                          ],
                        },
                      ],
                    }),
                  ]
                },
              }),
            },
            {
              name: 'codeExamples',
              type: 'array',
              fields: [
                {
                  name: 'title',
                  type: 'text',
                  required: true,
                },
                {
                  name: 'language',
                  type: 'select',
                  options: [
                    { label: 'HTML', value: 'html' },
                    { label: 'CSS', value: 'css' },
                    { label: 'JavaScript', value: 'javascript' },
                    { label: 'Python', value: 'python' },
                    { label: 'TypeScript', value: 'typescript' },
                    { label: 'JSON', value: 'json' },
                  ],
                  required: true,
                },
                {
                  name: 'code',
                  type: 'code',
                  required: true,
                },
                {
                  name: 'tryItYourself',
                  type: 'checkbox',
                  defaultValue: false,
                  admin: {
                    description: 'Enable "Try it Yourself" editor for this code example',
                  },
                },
                {
                  name: 'expectedOutput',
                  type: 'textarea',
                  admin: {
                    description: 'Expected output when code is run (optional)',
                    condition: (_, siblingData) => siblingData?.tryItYourself,
                  },
                },
              ],
            },
          ],
        },
        {
          label: 'Navigation',
          fields: [
            {
              name: 'previousTutorial',
              type: 'relationship',
              relationTo: 'tutorials',
              admin: {
                description: 'Previous tutorial in the sequence',
              },
            },
            {
              name: 'nextTutorial',
              type: 'relationship',
              relationTo: 'tutorials',
              admin: {
                description: 'Next tutorial in the sequence',
              },
            },
            {
              name: 'relatedTutorials',
              type: 'relationship',
              relationTo: 'tutorials',
              hasMany: true,
              admin: {
                description: 'Related tutorials to show at the end',
              },
            },
          ],
        },
      ],
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Featured image for the tutorial (recommended: 1200x630px for social sharing)',
        position: 'sidebar',
      },
    },
    {
      name: 'meta',
      label: 'SEO & Social Media',
      type: 'group',
      fields: [
        OverviewField({
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
          imagePath: 'meta.image',
        }),
        MetaTitleField({
          hasGenerateFn: true,
        }),
        MetaDescriptionField({}),
        MetaImageField({
          relationTo: 'media',
        }),
        {
          name: 'keywords',
          type: 'text',
          admin: {
            description:
              'SEO keywords separated by commas (e.g., "HTML, web development, beginner tutorial")',
          },
        },
        {
          name: 'canonicalUrl',
          type: 'text',
          admin: {
            description: 'Canonical URL if this content exists elsewhere (optional)',
          },
        },
        PreviewField({
          hasGenerateFn: true,
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
        }),
      ],
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        position: 'sidebar',
      },
    },
    ...slugField(),
  ],
  hooks: {
    afterChange: [revalidatePage],
    beforeChange: [populatePublishedAt],
  },
  versions: {
    drafts: {
      autosave: {
        interval: 100,
      },
      schedulePublish: true,
    },
    maxPerDoc: 50,
  },
}
