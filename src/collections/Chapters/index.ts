import type { CollectionConfig } from 'payload'

import { authenticated } from '../../access/authenticated'
import { authenticatedOrPublished } from '../../access/authenticatedOrPublished'
import { slugField } from '@/fields/slug'

import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField,
} from '@payloadcms/plugin-seo/fields'

export const Chapters: CollectionConfig<'chapters'> = {
  slug: 'chapters',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  defaultPopulate: {
    title: true,
    category: true,
    order: true,
  },
  admin: {
    defaultColumns: ['title', 'category', 'order', 'status', 'updatedAt'],
    useAsTitle: 'title',
  },
  fields: [
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description: 'Chapter title (e.g., "Getting Started with HTML")',
      },
    },
    {
      name: 'description',
      type: 'textarea',
      admin: {
        description: 'Brief description of what this chapter covers',
      },
    },
    {
      name: 'category',
      type: 'relationship',
      relationTo: 'categories',
      required: true,
      admin: {
        position: 'sidebar',
        description: 'Programming language or technology this chapter belongs to',
      },
    },
    {
      name: 'difficulty',
      type: 'select',
      options: [
        { label: 'Beginner', value: 'beginner' },
        { label: 'Intermediate', value: 'intermediate' },
        { label: 'Advanced', value: 'advanced' },
      ],
      defaultValue: 'beginner',
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'order',
      type: 'number',
      required: true,
      defaultValue: 1,
      admin: {
        description: 'Order of this chapter in the course (1, 2, 3...)',
        position: 'sidebar',
      },
    },
    {
      name: 'estimatedTime',
      type: 'text',
      admin: {
        description: 'Estimated time to complete (e.g., "2 hours")',
        position: 'sidebar',
      },
    },
    {
      name: 'icon',
      type: 'text',
      admin: {
        description: 'Lucide icon name (e.g., "play-circle", "book-open")',
        position: 'sidebar',
      },
    },
    {
      name: 'overview',
      type: 'richText',
      admin: {
        description: 'Detailed overview of what students will learn in this chapter',
      },
    },
    {
      name: 'learningObjectives',
      type: 'array',
      fields: [
        {
          name: 'objective',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        description: 'What students will be able to do after completing this chapter',
      },
    },
    {
      name: 'prerequisites',
      type: 'array',
      fields: [
        {
          name: 'prerequisite',
          type: 'text',
          required: true,
        },
      ],
      admin: {
        description: 'What students should know before starting this chapter',
      },
    },
    {
      name: 'previousChapter',
      type: 'relationship',
      relationTo: 'chapters',
      admin: {
        description: 'Previous chapter in the sequence',
        position: 'sidebar',
      },
    },
    {
      name: 'nextChapter',
      type: 'relationship',
      relationTo: 'chapters',
      admin: {
        description: 'Next chapter in the sequence',
        position: 'sidebar',
      },
    },
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        description: 'Feature this chapter on the homepage',
        position: 'sidebar',
      },
    },
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Featured image for the chapter (recommended: 1200x630px for social sharing)',
        position: 'sidebar',
      },
    },
    {
      name: 'meta',
      label: 'SEO & Social Media',
      type: 'group',
      fields: [
        OverviewField({
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
          imagePath: 'meta.image',
        }),
        MetaTitleField({
          hasGenerateFn: true,
        }),
        MetaDescriptionField({}),
        MetaImageField({
          relationTo: 'media',
        }),
        {
          name: 'keywords',
          type: 'text',
          admin: {
            description:
              'SEO keywords separated by commas (e.g., "HTML chapter, web development course, beginner")',
          },
        },
        {
          name: 'canonicalUrl',
          type: 'text',
          admin: {
            description: 'Canonical URL if this content exists elsewhere (optional)',
          },
        },
        PreviewField({
          hasGenerateFn: true,
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
        }),
      ],
    },
    ...slugField(),
  ],
  versions: {
    drafts: {
      autosave: {
        interval: 100,
      },
    },
    maxPerDoc: 50,
  },
}
