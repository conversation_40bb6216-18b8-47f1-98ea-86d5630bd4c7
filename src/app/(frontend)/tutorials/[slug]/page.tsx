import type { <PERSON><PERSON><PERSON> } from 'next'
import React from 'react'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { notFound } from 'next/navigation'
import RichText from '@/components/RichText'
import { Badge } from '@/components/ui/badge'
import { Clock } from 'lucide-react'
// // // // // import { TutorialLayout } from '@/components/TutorialLayout'

interface TutorialPageProps {
  params: Promise<{
    slug: string
  }>
}

async function getTutorial(slug: string) {
  const payload = await getPayload({ config: configPromise })

  try {
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        slug: {
          equals: slug,
        },
        _status: {
          equals: 'published',
        },
      },
      limit: 1,
      depth: 2,
    })

    if (tutorials.docs.length === 0) {
      return null
    }

    const tutorial = tutorials.docs[0]

    // Get sidebar items (tutorials in the same category)
    const sidebarTutorials = await payload.find({
      collection: 'tutorials',
      where: {
        category: {
          equals:
            typeof tutorial?.category === 'object' ? tutorial?.category?.id : tutorial?.category,
        },
        _status: {
          equals: 'published',
        },
      },
      sort: 'order',
      limit: 50,
    })

    const sidebarItems = sidebarTutorials.docs.map((t: any) => ({
      title: t.title,
      slug: t.slug,
      isActive: t.slug === slug,
    }))

    return {
      tutorial,
      sidebarItems,
    }
  } catch (error) {
    console.error('Error fetching tutorial:', error)
    return null
  }
}

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })

  try {
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
      },
    })

    return tutorials.docs.map((tutorial: any) => ({
      slug: tutorial.slug,
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

export default async function TutorialPage({ params }: TutorialPageProps) {
  const { slug } = await params
  const data = await getTutorial(slug)

  if (!data) {
    notFound()
  }

  const { tutorial, sidebarItems } = data

  // Difficulty colors
  const difficultyColors = {
    beginner: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    advanced: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  }

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Tutorial Header */}
        <header className="mb-8">
          {/* Badges and Meta Info */}
          <div className="flex items-center gap-3 mb-4">
            {tutorial?.category && typeof tutorial.category === 'object' && (
              <Badge
                variant="secondary"
                style={{
                  backgroundColor: (tutorial.category as any).color
                    ? (tutorial.category as any).color + '20'
                    : '#3B82F6' + '20',
                  color: (tutorial.category as any).color || '#3B82F6',
                }}
              >
                {(tutorial.category as any).title}
              </Badge>
            )}
            {tutorial?.difficulty && (
              <Badge
                className={
                  difficultyColors[tutorial.difficulty as keyof typeof difficultyColors] ||
                  difficultyColors.beginner
                }
              >
                {tutorial.difficulty}
              </Badge>
            )}
            {tutorial?.estimatedTime && (
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Clock className="h-4 w-4 mr-1" />
                {tutorial.estimatedTime}
              </div>
            )}
          </div>

          {/* Title */}
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {tutorial?.title}
          </h1>

          {/* Excerpt */}
          {tutorial?.excerpt && (
            <p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
              {tutorial.excerpt}
            </p>
          )}
        </header>

        {/* Tutorial Content */}
        <div className="prose dark:prose-invert max-w-none">
          {tutorial?.content ? (
            <RichText data={tutorial.content} enableGutter={false} />
          ) : (
            <p className="text-gray-500 italic">No content available for this tutorial.</p>
          )}
        </div>

        {/* Navigation (if available) */}
        {(tutorial?.previousTutorial || tutorial?.nextTutorial) && (
          <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              {tutorial?.previousTutorial && typeof tutorial.previousTutorial === 'object' ? (
                <a
                  href={`/tutorials/${(tutorial.previousTutorial as any).slug}`}
                  className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  ← Previous: {(tutorial.previousTutorial as any).title}
                </a>
              ) : (
                <div></div>
              )}

              {tutorial?.nextTutorial && typeof tutorial.nextTutorial === 'object' ? (
                <a
                  href={`/tutorials/${(tutorial.nextTutorial as any).slug}`}
                  className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Next: {(tutorial.nextTutorial as any).title} →
                </a>
              ) : (
                <div></div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: TutorialPageProps): Promise<Metadata> {
  const { slug } = await params
  const data = await getTutorial(slug)

  if (!data) {
    return {
      title: 'Tutorial Not Found | GuruDevs',
    }
  }

  const { tutorial } = data

  return {
    title: `${tutorial?.title || 'Tutorial'} | GuruDevs`,
    description:
      tutorial?.excerpt ||
      `Learn ${tutorial?.title || 'programming'} with our comprehensive tutorial.`,
    openGraph: {
      title: tutorial?.title || 'Tutorial',
      description:
        tutorial?.excerpt ||
        `Learn ${tutorial?.title || 'programming'} with our comprehensive tutorial.`,
      type: 'article',
    },
  }
}
