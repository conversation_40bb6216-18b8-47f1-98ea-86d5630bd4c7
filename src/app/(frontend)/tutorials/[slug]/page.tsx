import type { <PERSON>ada<PERSON> } from 'next'
import React from 'react'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { notFound } from 'next/navigation'
// // // // // import { TutorialLayout } from '@/components/TutorialLayout'

interface TutorialPageProps {
  params: Promise<{
    slug: string
  }>
}

async function getTutorial(slug: string) {
  const payload = await getPayload({ config: configPromise })

  try {
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        slug: {
          equals: slug,
        },
        _status: {
          equals: 'published',
        },
      },
      limit: 1,
      depth: 2,
    })

    if (tutorials.docs.length === 0) {
      return null
    }

    const tutorial = tutorials.docs[0]

    // Get sidebar items (tutorials in the same category)
    const sidebarTutorials = await payload.find({
      collection: 'tutorials',
      where: {
        category: {
          equals:
            typeof tutorial?.category === 'object' ? tutorial?.category?.id : tutorial?.category,
        },
        _status: {
          equals: 'published',
        },
      },
      sort: 'order',
      limit: 50,
    })

    const sidebarItems = sidebarTutorials.docs.map((t: any) => ({
      title: t.title,
      slug: t.slug,
      isActive: t.slug === slug,
    }))

    return {
      tutorial,
      sidebarItems,
    }
  } catch (error) {
    console.error('Error fetching tutorial:', error)
    return null
  }
}

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })

  try {
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
      },
    })

    return tutorials.docs.map((tutorial: any) => ({
      slug: tutorial.slug,
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

export default async function TutorialPage({ params }: TutorialPageProps) {
  const { slug } = await params
  const data = await getTutorial(slug)

  if (!data) {
    notFound()
  }

  const { tutorial, sidebarItems } = data

  // For now, create a simple tutorial display until we update TutorialLayout
  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">{tutorial?.title}</h1>
        {tutorial?.excerpt && (
          <p className="text-xl text-gray-600 dark:text-gray-300 mb-8">{tutorial.excerpt}</p>
        )}
        <div className="prose dark:prose-invert max-w-none">
          <p>Tutorial content will be displayed here.</p>
        </div>
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: TutorialPageProps): Promise<Metadata> {
  const { slug } = await params
  const data = await getTutorial(slug)

  if (!data) {
    return {
      title: 'Tutorial Not Found | GuruDevs',
    }
  }

  const { tutorial } = data

  return {
    title: `${tutorial?.title || 'Tutorial'} | GuruDevs`,
    description:
      tutorial?.excerpt ||
      `Learn ${tutorial?.title || 'programming'} with our comprehensive tutorial.`,
    openGraph: {
      title: tutorial?.title || 'Tutorial',
      description:
        tutorial?.excerpt ||
        `Learn ${tutorial?.title || 'programming'} with our comprehensive tutorial.`,
      type: 'article',
    },
  }
}
