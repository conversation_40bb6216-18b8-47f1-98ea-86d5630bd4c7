import type { <PERSON>ada<PERSON> } from 'next'
import React from 'react'
import { getPayload } from 'payload'
import configPromise from '@payload-config'
import { notFound } from 'next/navigation'
import RichText from '@/components/RichText'
import { Badge } from '@/components/ui/badge'
import { Clock } from 'lucide-react'
// // // // // import { TutorialLayout } from '@/components/TutorialLayout'

interface TutorialPageProps {
  params: Promise<{
    slug: string
  }>
}

async function getTutorial(slug: string) {
  const payload = await getPayload({ config: configPromise })

  try {
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        slug: {
          equals: slug,
        },
        _status: {
          equals: 'published',
        },
      },
      limit: 1,
      depth: 2,
      select: {
        title: true,
        slug: true,
        excerpt: true,
        content: true,
        category: true,
        difficulty: true,
        estimatedTime: true,
        previousTutorial: true,
        nextTutorial: true,
        relatedTutorials: true,
      },
    })

    if (tutorials.docs.length === 0) {
      return null
    }

    const tutorial = tutorials.docs[0]

    // Debug logging
    console.log('Fetched tutorial:', {
      title: tutorial.title,
      hasContent: !!tutorial.content,
      contentType: typeof tutorial.content,
      contentKeys: tutorial.content ? Object.keys(tutorial.content) : 'No content',
    })

    // Get sidebar items (tutorials in the same category)
    const sidebarTutorials = await payload.find({
      collection: 'tutorials',
      where: {
        category: {
          equals:
            typeof tutorial?.category === 'object' ? tutorial?.category?.id : tutorial?.category,
        },
        _status: {
          equals: 'published',
        },
      },
      sort: 'order',
      limit: 50,
    })

    const sidebarItems = sidebarTutorials.docs.map((t: any) => ({
      title: t.title,
      slug: t.slug,
      isActive: t.slug === slug,
    }))

    return {
      tutorial,
      sidebarItems,
    }
  } catch (error) {
    console.error('Error fetching tutorial:', error)
    return null
  }
}

export async function generateStaticParams() {
  const payload = await getPayload({ config: configPromise })

  try {
    const tutorials = await payload.find({
      collection: 'tutorials',
      where: {
        _status: {
          equals: 'published',
        },
      },
      limit: 1000,
      pagination: false,
      select: {
        slug: true,
      },
    })

    return tutorials.docs.map((tutorial: any) => ({
      slug: tutorial.slug,
    }))
  } catch (error) {
    console.error('Error generating static params:', error)
    return []
  }
}

export default async function TutorialPage({ params }: TutorialPageProps) {
  const { slug } = await params
  const data = await getTutorial(slug)

  if (!data) {
    notFound()
  }

  const { tutorial, sidebarItems } = data

  // Difficulty colors
  const difficultyColors = {
    beginner: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    intermediate: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300',
    advanced: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
  }

  // Extract text content for debugging
  const extractTextContent = (node: any): string => {
    if (!node) return ''
    if (typeof node === 'string') return node
    if (node.text) return node.text
    if (node.children && Array.isArray(node.children)) {
      return node.children.map(extractTextContent).join(' ')
    }
    return ''
  }

  const textContent = tutorial?.content ? extractTextContent(tutorial.content.root) : ''

  // Convert markdown-like text to HTML with better processing
  const convertToHTML = (text: string): string => {
    // First, split into lines for better processing
    const lines = text.split('\n')
    const processedLines: string[] = []

    for (let i = 0; i < lines.length; i++) {
      let line = lines[i]

      // Skip empty lines initially
      if (!line.trim()) {
        processedLines.push('')
        continue
      }

      // Headers
      if (line.match(/^#{1,6}\s+/)) {
        const headerMatch = line.match(/^(#{1,6})\s+(.+)$/)
        if (headerMatch) {
          const level = headerMatch[1].length
          const text = headerMatch[2]
          const classes = {
            1: 'text-3xl font-bold mb-6 mt-8',
            2: 'text-2xl font-bold mb-4 mt-8',
            3: 'text-xl font-semibold mb-4 mt-6',
            4: 'text-lg font-semibold mb-3 mt-5',
            5: 'text-base font-semibold mb-2 mt-4',
            6: 'text-sm font-semibold mb-2 mt-3',
          }
          processedLines.push(
            `<h${level} class="${classes[level as keyof typeof classes]}">${text}</h${level}>`,
          )
          continue
        }
      }

      // Horizontal rules
      if (line.match(/^---+$/)) {
        processedLines.push('<hr class="border-gray-300 dark:border-gray-600 my-8" />')
        continue
      }

      // Tables
      if (line.includes('|') && line.trim().startsWith('|') && line.trim().endsWith('|')) {
        const cells = line
          .split('|')
          .slice(1, -1)
          .map((cell) => cell.trim())
        if (cells.length > 0 && !cells.every((cell) => cell.match(/^[\s\-:]*$/))) {
          const cellsHtml = cells
            .map(
              (cell) =>
                `<td class="border border-gray-300 dark:border-gray-600 px-4 py-2">${processInlineMarkdown(cell)}</td>`,
            )
            .join('')
          processedLines.push(`<tr>${cellsHtml}</tr>`)
          continue
        }
      }

      // Regular paragraphs
      if (line.trim()) {
        processedLines.push(`<p class="mb-4">${processInlineMarkdown(line)}</p>`)
      }
    }

    return processedLines.join('\n')
  }

  // Process inline markdown (bold, italic, code, links)
  const processInlineMarkdown = (text: string): string => {
    return (
      text
        // Bold
        .replace(/\*\*(.*?)\*\*/g, '<strong class="font-semibold">$1</strong>')
        // Italic
        .replace(/\*(.*?)\*/g, '<em class="italic">$1</em>')
        // Inline code
        .replace(
          /`(.*?)`/g,
          '<code class="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm font-mono">$1</code>',
        )
        // Links
        .replace(
          /\[([^\]]*)\]\(([^\)]*)\)/g,
          '<a href="$2" class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 underline">$1</a>',
        )
    )
  }

  // Wrap table rows in proper table structure
  const wrapTables = (html: string): string => {
    return html.replace(
      /(<tr>.*?<\/tr>(?:\s*<tr>.*?<\/tr>)*)/gims,
      '<table class="min-w-full border-collapse border border-gray-300 dark:border-gray-600 my-6"><tbody>$1</tbody></table>',
    )
  }

  const htmlContent = textContent ? wrapTables(convertToHTML(textContent)) : ''

  return (
    <div className="min-h-screen py-12">
      <div className="container mx-auto px-4 max-w-4xl">
        {/* Tutorial Header */}
        <header className="mb-8">
          {/* Badges and Meta Info */}
          <div className="flex items-center gap-3 mb-4">
            {tutorial?.category && typeof tutorial.category === 'object' && (
              <Badge
                variant="secondary"
                style={{
                  backgroundColor: (tutorial.category as any).color
                    ? (tutorial.category as any).color + '20'
                    : '#3B82F6' + '20',
                  color: (tutorial.category as any).color || '#3B82F6',
                }}
              >
                {(tutorial.category as any).title}
              </Badge>
            )}
            {tutorial?.difficulty && (
              <Badge
                className={
                  difficultyColors[tutorial.difficulty as keyof typeof difficultyColors] ||
                  difficultyColors.beginner
                }
              >
                {tutorial.difficulty}
              </Badge>
            )}
            {tutorial?.estimatedTime && (
              <div className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                <Clock className="h-4 w-4 mr-1" />
                {tutorial.estimatedTime}
              </div>
            )}
          </div>

          {/* Title */}
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-4">
            {tutorial?.title}
          </h1>

          {/* Excerpt */}
          {tutorial?.excerpt && (
            <p className="text-xl text-gray-600 dark:text-gray-300 leading-relaxed mb-6">
              {tutorial.excerpt}
            </p>
          )}
        </header>

        {/* Tutorial Content */}
        <div className="prose dark:prose-invert max-w-none">
          {tutorial?.content ? (
            <>
              <div
                className="tutorial-content"
                dangerouslySetInnerHTML={{
                  __html: htmlContent,
                }}
              />
            </>
          ) : (
            <p className="text-gray-500 italic">No content available for this tutorial.</p>
          )}
        </div>

        {/* Navigation (if available) */}
        {(tutorial?.previousTutorial || tutorial?.nextTutorial) && (
          <div className="mt-12 pt-8 border-t border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center">
              {tutorial?.previousTutorial && typeof tutorial.previousTutorial === 'object' ? (
                <a
                  href={`/tutorials/${(tutorial.previousTutorial as any).slug}`}
                  className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  ← Previous: {(tutorial.previousTutorial as any).title}
                </a>
              ) : (
                <div></div>
              )}

              {tutorial?.nextTutorial && typeof tutorial.nextTutorial === 'object' ? (
                <a
                  href={`/tutorials/${(tutorial.nextTutorial as any).slug}`}
                  className="flex items-center text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                >
                  Next: {(tutorial.nextTutorial as any).title} →
                </a>
              ) : (
                <div></div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export async function generateMetadata({ params }: TutorialPageProps): Promise<Metadata> {
  const { slug } = await params
  const data = await getTutorial(slug)

  if (!data) {
    return {
      title: 'Tutorial Not Found | GuruDevs',
    }
  }

  const { tutorial } = data

  return {
    title: `${tutorial?.title || 'Tutorial'} | GuruDevs`,
    description:
      tutorial?.excerpt ||
      `Learn ${tutorial?.title || 'programming'} with our comprehensive tutorial.`,
    openGraph: {
      title: tutorial?.title || 'Tutorial',
      description:
        tutorial?.excerpt ||
        `Learn ${tutorial?.title || 'programming'} with our comprehensive tutorial.`,
      type: 'article',
    },
  }
}
